version: '3.9'

services:
  traefik:
    image: traefik:v3.0
    ports:
      - 80:80
      - 443:443
    restart: always
    networks:
      - proxy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - letsencrypt:/letsencrypt
      #- /var/log:/var/log
    command:
      - --api.dashboard=false
      #- --log.level=INFO
      #- --log.filepath=/var/log/traefik.log
      #- --accesslog=true
      #- --accesslog.filepath=/var/log/traefik-access.log
      - --providers.docker.network=proxy
      - --providers.docker.exposedByDefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entryPoints.web.http.redirections.entrypoint.scheme=https
      - --entrypoints.websecure.address=:443
      - --entrypoints.websecure.asDefault=true
      - --entrypoints.websecure.http.tls.certresolver=myresolver
      - --certificatesresolvers.myresolver.acme.email=<EMAIL>
      - --certificatesresolvers.myresolver.acme.tlschallenge=true
      - --certificatesresolvers.myresolver.acme.storage=/letsencrypt/acme.json
    labels:
      # - traefik.enable=true
      # - traefik.http.routers.mydashboard.rule=Host(`traefik2.tobiasstaehle.de`)
      # - traefik.http.routers.mydashboard.service=api@internal
      # - traefik.http.routers.mydashboard.middlewares=myauth -- admin Sich1234!
      - traefik.http.middlewares.myauth.basicauth.users=admin:$apr1$9cUtEZBT$7zINs.OlEdrnUTk75cKcx.
      
  static:
    # nginx config
    image: nginx
    restart: always
    volumes:
        - ./files/Frontend_Documentstore/build:/usr/share/nginx/html:ro
        - ./files/Frontend_Documentstore/nginx.conf:/etc/nginx/nginx.conf:ro
    container_name: static-files
    #restart: unless-stopped
    networks:
        - proxy
    labels:
        - traefik.enable=true
        # Match on the hostname and the path
        - traefik.http.routers.nginx.rule=Host(`boe-stage.winkler-software.io`)
        # - traefik.http.routers.nginx.rule=Host(`boeapp.tobiasstaehle.de`)
        - traefik.http.routers.nginx.entrypoints=websecure
        - traefik.http.routers.nginx.tls.certresolver=myresolver
        #- traefik.http.routers.nginx.tls=true
        #- traefik.http.routers.nginx.tls.certresolver=le
        #- traefik.http.services.nginx.loadbalancer.server.port=80

        # tell Traefik which middlewares we want to use on this container
        #- traefik.http.routers.nginx.middlewares=gzip

  backend:
    build: .
    command: bash -c 'uvicorn app.main:app --host 0.0.0.0'
    restart: always
    volumes:
      - .:/app
    expose:
      - 8000
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - SMTP_MAIL=${SMTP_MAIL}
      - SMTP_PW=${SMTP_PW}    # $$ to have one single $
      - API_DOMAIN=${API_DOMAIN}
      - WEBSITE_DOMAIN=${WEBSITE_DOMAIN}
      - SUPERTOKENS_APIKEY=${SUPERTOKENS_APIKEY}
      - SUPERTOKENS_URI=${SUPERTOKENS_URI}
      - HIDRIVE_BASE_PATH=${HIDRIVE_BASE_PATH}
      - BOEAPP_WERBUNG_FOLDER_ID=${BOEAPP_WERBUNG_FOLDER_ID}

      - HIDRIVE_BASE_IDS_ROOT=${HIDRIVE_BASE_IDS_ROOT}
      - HIDRIVE_BASE_IDS_HAUSVERWALTUNG=${HIDRIVE_BASE_IDS_HAUSVERWALTUNG}
      - HIDRIVE_BASE_IDS_EXTERN_BAUARBEITER=${HIDRIVE_BASE_IDS_EXTERN_BAUARBEITER}
      - ENV=prod

      #- SUPERTOKENS_DEBUG=1
    depends_on:
      - db
      - supertokens
    networks:
      - database_and_supertokens
      - proxy
    labels:
        - traefik.enable=true
        # Match on the hostname and the path
        - traefik.http.routers.boeapp.rule=Host(`boe-back.winkler-software.io`)
        #- traefik.http.routers.boeapp.rule=Host(`boeappbackend.tobiasstaehle.de`)
        - traefik.http.routers.boeapp.entrypoints=websecure
        - traefik.http.routers.boeapp.tls.certresolver=myresolver
  db:
    image: postgres:13-alpine
    restart: always
    volumes:
      - /postgres_data_test:/var/lib/postgresql/data/
    expose:
      - 5432
    
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    networks:
      - database_and_supertokens
  
  supertokens:
    image: registry.supertokens.io/supertokens/supertokens-postgresql:6.0
    restart: always
    depends_on:
      - db
    ports:
      - 3567:3567
    environment:
      - POSTGRESQL_CONNECTION_URI=${DATABASE_URL}
      - API_KEYS=${SUPERTOKENS_APIKEY}
    networks:
      - database_and_supertokens

  
networks:
  proxy:
    name: proxy
  database_and_supertokens:
    name: database_and_supertokens
  
volumes:
  letsencrypt:
    name: letsencrypt
