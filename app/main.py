# app/main.py
import os
import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List
from multiprocessing import Queue
from multiprocessing.managers import BaseManager
from concurrent.futures.process import ProcessPoolExecutor
import pytz

# FastAPI imports
from fastapi import Fast<PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

# Scheduler imports
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.cron import CronTrigger

# Third-party imports
from slowapi.errors import RateLimitExceeded
from slowapi import _rate_limit_exceeded_handler
from supertokens_python import get_all_cors_headers
from supertokens_python.framework.fastapi import get_middleware
from pydantic import BaseModel

# Local imports
from app.origins import origins
from app.db import engine, metadata, database
from app import limiter
from app.logging_logs import general_logging
from app.middleware import log_middleware

# Service imports
from app.documents import services as document_services, models
from app.hidrive_plugin import services as hidrive_services
from app.usermanagement import services as user_services
from app.api_security import services as security_services
from app.documents.service_daily_unread_notifications import send_daily_unread_notifications, get_users_with_unread_documents_today, is_email_notifications_enabled
from app.urlaubsantrag.scheduled_tasks import add_monthly_vacation_days, reset_vacation_days

# File tree imports
from app.filetree.filetree_structure.scheduled_tasks import (
    check_for_updates, 
    get_all_files, 
    scheduled_renew_hidrive_token, 
    scheduled_task_save_tree
)
from app.filetree.filetree_structure.tree_file_interactions import read_json_from_file
from app.filetree.filetree_structure.tree_initialization import Tree
from app.filetree.hidrive.hidrive import HidriveAPI
import app.filetree.filetree_structure.global_vars as global_vars

# Router imports
from app.zeiterfassung.api import arbeitzeit_router
from app.usermanagement.api import auth_router, user_router
from app.documents.api import doc_router
from app.hausverwaltung.api import hausverwaltung_router
from app.hidrive_plugin.api import hidrive_router
from app.api_security.api import security_router
from app.advertisement.api import ads_router
from app.privacyPolicy.api import privacy_router
from app.logging_logs.api import logging_router
from app.filetree.hidrive.api import hidriveRouter as hidrive_router_v2
from app.filetree.api import docRouter as document_router_v2
from app.urlaubsantrag.api import urlaubsantrag_router

# SuperTokens imports
from app.superTokens_setup.superTokens_init import init_supertokens
from app.superTokens_setup.session_patch import apply_patch

# Models imports
import app.usermanagement.models
from app.documents.models import Documents

# Initialize database
metadata.create_all(engine)

# Initialize FastAPI app
app = FastAPI(docs_url=None, redoc_url=None)
general_logging.application_logger.info("Starting Backend ...")

# Middleware setup
app.add_middleware(BaseHTTPMiddleware, dispatch=log_middleware)
app.state.limiter = limiter.limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# SuperTokens setup
init_supertokens()
apply_patch()
app.add_middleware(get_middleware())

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "PUT", "POST", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["Content-Type"] + get_all_cors_headers(),
)

# Multiprocessing manager setup
class CustomManager(BaseManager):
    pass

CustomManager.register('Tree', Tree)
CustomManager.register('Queue_V1', Queue)
CustomManager.register('HidriveAPI', HidriveAPI)

manager = CustomManager()

# Global scheduler - will be initialized in startup
scheduler = None


@app.get("/public/ping")
async def hello_public():
    return {"message": "Backend available!"}


# Include routers
app.include_router(auth_router, prefix='/auth')
app.include_router(user_router, prefix='/user')
app.include_router(hidrive_router_v2, prefix='/hidrive_v2')
app.include_router(document_router_v2, prefix='/documents_v2')
app.include_router(hausverwaltung_router, prefix='/hausverwaltung')
app.include_router(hidrive_router, prefix='/hidrive')
app.include_router(security_router, prefix='/security')
app.include_router(ads_router, prefix='/ads')
app.include_router(privacy_router, prefix='/privacy')
app.include_router(doc_router, prefix='/doc')
app.include_router(logging_router, prefix='/logging')
app.include_router(arbeitzeit_router, prefix='/arbeitzeit')
app.include_router(urlaubsantrag_router, prefix='/urlaubsantrag')


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    exc_str = f'{exc}'.replace('\n', ' ').replace('   ', ' ')
    print(exc_str, flush=True)
    content = {'status_code': 10422, 'message': exc_str, 'data': None}
    return JSONResponse(content=content, status_code=status.HTTP_422_UNPROCESSABLE_ENTITY)


@app.on_event("startup")
async def startup():
    global scheduler
    if os.getenv("ENV") == "prod":
        import builtins
        print('PROD MODE ON!!')
        builtins.print = lambda *args, **kwargs: None
    print("Starting application startup...", flush=True)
    
    # Set Berlin timezone
    berlin_tz = pytz.timezone('Europe/Berlin')
    
    # 1. Database connection with retries
    max_db_retries = 5
    db_retry_delay = 2
    
    for attempt in range(max_db_retries):
        try:
            if not database.is_connected:
                await database.connect()
                print("Database connected successfully", flush=True)
            break
        except Exception as e:
            if attempt < max_db_retries - 1:
                print(f"Database connection attempt {attempt + 1} failed: {str(e)}. Retrying in {db_retry_delay} seconds...", flush=True)
                await asyncio.sleep(db_retry_delay)
            else:
                print(f"Database connection failed after {max_db_retries} attempts: {str(e)}", flush=True)
                raise

    # 2. Initialize process pool executor
    app.state.executor = ProcessPoolExecutor()
    print("Process pool executor initialized", flush=True)

    # 3. Start multiprocessing manager and initialize file tree
    manager.start()
    json_tree = read_json_from_file("tree.json")
    global_vars.tree_store_v1 = manager.Tree(tree_data=json_tree)
    global_vars.queue = manager.Queue_V1()
    global_vars.hidriveApi = manager.HidriveAPI()
    print("Multiprocessing manager and file tree initialized", flush=True)

    # 4. Initialize and start scheduler
    scheduler = AsyncIOScheduler(timezone=berlin_tz)
    
    # Add regular interval jobs
    scheduler.add_job(scheduled_task_save_tree, 'interval', seconds=30, id='save_tree')
    scheduler.add_job(scheduled_renew_hidrive_token, 'interval', seconds=60*10, id='renew_token')
    scheduler.add_job(check_for_updates, 'interval', seconds=15, id='check_updates')
    
    # Add immediate jobs with delays
    next_token_renewal = datetime.now(berlin_tz) + timedelta(seconds=4)
    scheduler.add_job(
        scheduled_renew_hidrive_token, 
        DateTrigger(run_date=next_token_renewal),
        id='initial_renew_token'
    )
    print(f"Scheduled initial token renewal for: {next_token_renewal.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    
    # Initialize file tree if needed
    if global_vars.tree_store_v1.get_initialized_from_data() == False:
        initial_file_get = datetime.now(berlin_tz) + timedelta(seconds=10)
        scheduler.add_job(
            get_all_files,
            DateTrigger(run_date=initial_file_get),
            id='initial_get_files'
        )
        print(f"Scheduled initial file tree initialization for: {initial_file_get.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    
    # Add vacation days jobs
    now = datetime.now(berlin_tz)
    reset_year = now.year if now.month < 1 or (now.month == 1 and now.day < 1) else now.year + 1
    reset_date = datetime(reset_year, 1, 1, 0, 1, tzinfo=berlin_tz)
    
    scheduler.add_job(
        reset_vacation_days,
        DateTrigger(run_date=reset_date),
        misfire_grace_time=30,
        id='vacation_days_reset'
    )
    
    scheduler.add_job(
        add_monthly_vacation_days,
        CronTrigger(day=1, hour=0, minute=1, timezone=berlin_tz),
        misfire_grace_time=30,
        id='monthly_vacation_days_add'
    )

    scheduler.add_job(
        send_daily_unread_notifications, 
        CronTrigger(hour=18, minute=0, timezone=berlin_tz),
        misfire_grace_time=30,
        id='daily_notifications'
    )
    
    # Start the scheduler
    scheduler.start()
    print("Scheduler started with all jobs", flush=True)

    await asyncio.sleep(2)
    print("SuperTokens initialization wait completed", flush=True)

    # 6. Initialize users and security
    try:
        await user_services.createInitialUser()
        print("Initial user created successfully", flush=True)
    except Exception as e:
        print(f"Error during initial user creation: {e}", flush=True)
        general_logging.application_logger.error(f"Failed to create initial user: {e}")

    try:
        await security_services.initial_Endpoint_Registration(app=app)
        print("Endpoint registration completed successfully", flush=True)
    except Exception as e:
        print(f"Error during endpoint registration: {e}", flush=True)
        general_logging.application_logger.error(f"Failed to register endpoints with SuperTokens: {e}")

    print("Application startup completed successfully!", flush=True)


@app.on_event("shutdown")
async def shutdown():
    global scheduler
    
    print("Starting application shutdown...", flush=True)
    
    # 1. Shutdown scheduler
    if scheduler:
        scheduler.shutdown()
        print("Scheduler shut down", flush=True)
    
    # 2. Shutdown multiprocessing manager
    manager.shutdown()
    print("Multiprocessing manager shut down", flush=True)
    
    # 3. Shutdown process pool executor
    if hasattr(app.state, 'executor'):
        app.state.executor.shutdown()
        print("Process pool executor shut down", flush=True)
    
    # 4. Disconnect database
    if database.is_connected:
        await database.disconnect()
        print("Database disconnected", flush=True)
    
    print("Application shutdown completed!", flush=True)
